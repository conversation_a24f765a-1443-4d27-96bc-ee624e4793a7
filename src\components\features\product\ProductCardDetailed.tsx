"use client";

import { memo } from "react";
import type { Product } from "@/types/ecommerce";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Icon } from "@iconify/react";
import { useImageHandler } from "@/hooks/use-image-handler";
import { useKeyboardNavigation } from "@/hooks/use-keyboard-navigation";
import { formatTimeAgo } from "@/utils/date-utils";
import { validateImageUrl } from "@/utils/image-utils";
import { SmartImage } from "@/components/common/SmartImage";

interface ProductCardDetailedProps {
  product: Product;
  onViewDetails: () => void;
  className?: string;
  showAnimations?: boolean;
}

const ProductCardDetailed = memo(function ProductCardDetailed({
  product,
  onViewDetails,
  className = "",
  showAnimations = true,
}: ProductCardDetailedProps) {
  const { handleImageError, handleImageLoad } = useImageHandler();
  const { handleKeyDown } = useKeyboardNavigation({
    onEnter: onViewDetails,
    onSpace: onViewDetails,
  });

  const isMobileCompact = className?.includes("mobile-compact");

  return (
    <article
      className={`bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-xl hover:-translate-y-1 sm:hover:-translate-y-2 transition-all duration-300 cursor-pointer group flex flex-col h-full ${className}`}
      onClick={onViewDetails}
      role="button"
      tabIndex={0}
      onKeyDown={handleKeyDown}
      aria-label={`View details for ${product.title}`}
    >
      {/* Product Image */}
      <div
        className={`aspect-[4/3] ${
          isMobileCompact
            ? "mx-1 mt-1"
            : "mx-1 sm:mx-2 md:mx-3 lg:mx-4 mt-1 sm:mt-2 md:mt-3"
        } bg-white relative overflow-hidden flex-shrink-0 rounded-lg`}
      >
        {product.images && product.images.length > 0 ? (
          <SmartImage
            src={validateImageUrl(product.images[0])}
            alt={product.title}
            width={300}
            height={225}
            className={`w-full h-full object-cover bg-white rounded-lg ${
              showAnimations
                ? "group-hover:scale-105 sm:group-hover:scale-110 transition-transform duration-500"
                : ""
            }`}
            onError={handleImageError}
            onLoad={handleImageLoad}
            loading="lazy"
          />
        ) : (
          <div className="w-full h-full bg-gray-100 flex flex-col items-center justify-center rounded-lg">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-200 rounded-lg flex items-center justify-center mb-2 sm:mb-3">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <span className="text-xs sm:text-sm text-gray-500">
              No Image Available
            </span>
          </div>
        )}

        {product.featured && (
          <div
            className={`absolute top-2 sm:top-3 left-2 sm:left-3 bg-gradient-to-r from-teal-500 to-teal-600 text-white px-2 sm:px-3 py-0.5 sm:py-1 text-xs font-semibold rounded-full shadow-lg ${
              showAnimations ? "animate-scaleIn" : ""
            }`}
          >
            Featured
          </div>
        )}
      </div>

      {/* Product Info */}
      <div
        className={`${
          isMobileCompact ? "p-2" : "p-3 sm:p-4 md:p-5"
        } flex-1 flex flex-col`}
      >
        <h3
          className={`font-semibold text-gray-800 ${
            isMobileCompact
              ? "mb-1 text-sm"
              : "mb-2 text-sm sm:text-base md:text-lg"
          } group-hover:text-teal-600 transition-colors line-clamp-2 overflow-hidden leading-tight`}
        >
          {product.title}
        </h3>

        {/* Badges Section */}
        <div
          className={`flex items-center gap-1 ${
            isMobileCompact ? "mb-1" : "mb-2 sm:mb-3"
          } flex-wrap`}
        >
          <Badge
            variant="secondary"
            className={`${
              isMobileCompact ? "text-xs px-1 py-0" : "text-xs px-2 py-0.5"
            } bg-green-100 text-green-800 border-green-200`}
          >
            {product.condition}
          </Badge>
          {product.brand && !isMobileCompact && (
            <Badge
              variant="outline"
              className="text-xs px-2 py-0.5 hidden sm:inline-flex"
            >
              <Icon icon="lucide:package" className="h-3 w-3 mr-1" />
              {product.brand}
            </Badge>
          )}
          {product.subcategory && !isMobileCompact && (
            <Badge
              variant="outline"
              className="text-xs px-2 py-0.5 hidden md:inline-flex"
            >
              <Icon icon="lucide:tag" className="h-3 w-3 mr-1" />
              {product.subcategory}
            </Badge>
          )}
        </div>

        <div
          className={`${
            isMobileCompact
              ? "text-lg mb-1"
              : "text-lg sm:text-xl md:text-2xl mb-2 sm:mb-3"
          } font-bold text-gray-900 truncate`}
        >
          {product.currency}
          {product.price.toLocaleString()}
        </div>

        {/* Location, Time, and Views */}
        <div
          className={`flex items-center gap-2 ${
            isMobileCompact ? "text-xs mb-1" : "text-sm mb-3"
          } text-gray-600 flex-wrap`}
        >
          <div className="flex items-center gap-1 flex-1 min-w-0">
            <Icon
              icon="lucide:map-pin"
              className={`${
                isMobileCompact ? "h-3 w-3" : "h-4 w-4"
              } flex-shrink-0`}
              aria-hidden="true"
            />
            <span className="truncate">{product.location}</span>
          </div>
          {!isMobileCompact && (
            <div className="flex items-center gap-1 flex-shrink-0">
              <Icon
                icon="lucide:clock"
                className="h-4 w-4"
                aria-hidden="true"
              />
              <span className="whitespace-nowrap">
                {formatTimeAgo(product.postedAt)}
              </span>
            </div>
          )}
          {product.views && !isMobileCompact && (
            <div className="flex items-center gap-1 flex-shrink-0">
              <Icon icon="lucide:eye" className="h-4 w-4" aria-hidden="true" />
              <span className="whitespace-nowrap">{product.views} views</span>
            </div>
          )}
        </div>

        {/* Features Section (if available) */}
        {product.features && product.features.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {product.features.slice(0, 3).map((feature, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs text-gray-600"
                >
                  {feature}
                </Badge>
              ))}
              {product.features.length > 3 && (
                <Badge variant="outline" className="text-xs text-gray-500">
                  +{product.features.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Seller Info */}
        {!isMobileCompact && (
          <div className="flex items-center gap-2 text-sm text-gray-600 mb-4 min-w-0">
            <Icon
              icon="lucide:user"
              className="h-4 w-4 flex-shrink-0"
              aria-hidden="true"
            />
            <span className="truncate">By {product.seller.name}</span>
            {product.seller.rating && (
              <span className="text-yellow-500 font-medium flex-shrink-0">
                ★ {product.seller.rating}
              </span>
            )}
          </div>
        )}

        {/* View Details Button */}
        <Button
          className={`w-full bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white font-semibold ${
            isMobileCompact
              ? "py-1.5 text-xs"
              : "py-2 sm:py-2.5 text-sm sm:text-base"
          } rounded-lg transition-all duration-200 transform hover:scale-105 mt-auto min-h-[44px] flex items-center justify-center`}
          onClick={(e) => {
            e.stopPropagation();
            onViewDetails();
          }}
          aria-label={`View details for ${product.title}`}
        >
          {isMobileCompact ? "View" : "View Details"}
        </Button>
      </div>
    </article>
  );
});

export default ProductCardDetailed;
