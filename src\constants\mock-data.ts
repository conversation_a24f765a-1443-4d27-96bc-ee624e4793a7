import { Category, Product, FilterOptions } from "@/types/ecommerce";
import { generateSlug } from "@/utils/slug-utils";

// Fixed UUIDs for consistent mock data
const MOCK_UUIDS = {
  // Product IDs
  PRODUCT_1: "550e8400-e29b-41d4-a716-************",
  PRODUCT_2: "550e8400-e29b-41d4-a716-************",
  PRODUCT_3: "550e8400-e29b-41d4-a716-************",
  PRODUCT_4: "550e8400-e29b-41d4-a716-************",
  PRODUCT_5: "550e8400-e29b-41d4-a716-************",
  PRODUCT_PROPERTY_1: "550e8400-e29b-41d4-a716-************",
  PRODUCT_PROPERTY_2: "550e8400-e29b-41d4-a716-************",
  PRODUCT_VEHICLE_1: "550e8400-e29b-41d4-a716-************",
  PRODUCT_ELECTRONICS_1: "550e8400-e29b-41d4-a716-************",
  PRODUCT_HOME_1: "550e8400-e29b-41d4-a716-************",
  PRODUCT_JOB_1: "550e8400-e29b-41d4-a716-************",

  // Seller IDs
  SELLER_1: "660e8400-e29b-41d4-a716-************",
  SELLER_2: "660e8400-e29b-41d4-a716-************",
  SELLER_3: "660e8400-e29b-41d4-a716-************",
  SELLER_4: "660e8400-e29b-41d4-a716-************",
  SELLER_PROP_1: "660e8400-e29b-41d4-a716-************",
  SELLER_PROP_2: "660e8400-e29b-41d4-a716-************",
  SELLER_VEH_1: "660e8400-e29b-41d4-a716-************",
  SELLER_ELEC_1: "660e8400-e29b-41d4-a716-************",
  SELLER_HOME_1: "660e8400-e29b-41d4-a716-************",
  SELLER_COMPANY_1: "660e8400-e29b-41d4-a716-************",
};

// Mock Categories Data
export const mockCategories: Category[] = [
  {
    id: "art-crafts",
    name: "Art & Crafts",
    slug: "art-crafts",
    description: "Handmade art pieces, crafts, and creative items",
    icon: "🎨",
    productCount: 156,
    subcategories: [
      { id: "painting", name: "Painting", slug: "painting", productCount: 45 },
      {
        id: "sculpture",
        name: "Sculpture",
        slug: "sculpture",
        productCount: 23,
      },
      { id: "pottery", name: "Pottery", slug: "pottery", productCount: 34 },
      { id: "canvas", name: "Canvas Art", slug: "canvas", productCount: 54 },
    ],
  },
  {
    id: "bicycle-accessories",
    name: "Bicycle & Accessories",
    slug: "bicycle-accessories",
    description: "Bicycles and cycling accessories",
    icon: "🚴",
    productCount: 89,
    subcategories: [
      {
        id: "mountain-bikes",
        name: "Mountain Bikes",
        slug: "mountain-bikes",
        productCount: 25,
      },
      {
        id: "road-bikes",
        name: "Road Bikes",
        slug: "road-bikes",
        productCount: 20,
      },
      {
        id: "electric-bikes",
        name: "Electric Bikes",
        slug: "electric-bikes",
        productCount: 15,
      },
      {
        id: "bike-accessories",
        name: "Bike Accessories",
        slug: "bike-accessories",
        productCount: 29,
      },
    ],
  },
  {
    id: "books-magazines",
    name: "Books & Magazines",
    slug: "books-magazines",
    description: "Books, magazines, and reading materials",
    icon: "📚",
    productCount: 234,
    subcategories: [
      {
        id: "fiction",
        name: "Fiction Books",
        slug: "fiction",
        productCount: 89,
      },
      {
        id: "non-fiction",
        name: "Non-Fiction Books",
        slug: "non-fiction",
        productCount: 67,
      },
      {
        id: "textbooks",
        name: "Textbooks",
        slug: "textbooks",
        productCount: 45,
      },
      {
        id: "magazines",
        name: "Magazines",
        slug: "magazines",
        productCount: 33,
      },
    ],
  },
  {
    id: "building-construction",
    name: "Building & Construction",
    slug: "building-construction",
    description: "Construction materials and building supplies",
    icon: "🔨",
    productCount: 145,
    subcategories: [
      {
        id: "hand-tools",
        name: "Hand Tools",
        slug: "hand-tools",
        productCount: 45,
      },
      {
        id: "power-tools",
        name: "Power Tools",
        slug: "power-tools",
        productCount: 35,
      },
      {
        id: "building-materials",
        name: "Building Materials",
        slug: "building-materials",
        productCount: 40,
      },
      {
        id: "safety-equipment",
        name: "Safety Equipment",
        slug: "safety-equipment",
        productCount: 25,
      },
    ],
  },
  {
    id: "business",
    name: "Business",
    slug: "business",
    description: "Business opportunities and services",
    icon: "💼",
    productCount: 298,
    subcategories: [
      {
        id: "consulting",
        name: "Consulting Services",
        slug: "consulting",
        productCount: 78,
      },
      {
        id: "office-equipment",
        name: "Office Equipment",
        slug: "office-equipment",
        productCount: 89,
      },
      {
        id: "business-opportunities",
        name: "Business Opportunities",
        slug: "business-opportunities",
        productCount: 53,
      },
      {
        id: "marketing-services",
        name: "Marketing Services",
        slug: "marketing-services",
        productCount: 78,
      },
    ],
  },
  {
    id: "clothes-fashions",
    name: "Clothes & Fashions",
    slug: "clothes-fashions",
    description: "Clothing and fashion items",
    icon: "👗",
    productCount: 456,
    subcategories: [
      {
        id: "mens-clothing",
        name: "Men's Clothing",
        slug: "mens-clothing",
        productCount: 156,
      },
      {
        id: "womens-clothing",
        name: "Women's Clothing",
        slug: "womens-clothing",
        productCount: 200,
      },
      {
        id: "kids-clothing",
        name: "Kids Clothing",
        slug: "kids-clothing",
        productCount: 50,
      },
      {
        id: "shoes-footwear",
        name: "Shoes & Footwear",
        slug: "shoes-footwear",
        productCount: 50,
      },
    ],
  },
  {
    id: "events-tickets",
    name: "Events & Tickets",
    slug: "events-tickets",
    description: "Event tickets and entertainment",
    icon: "🎫",
    productCount: 78,
    subcategories: [
      { id: "concerts", name: "Concerts", slug: "concerts", productCount: 25 },
      { id: "sports", name: "Sports Events", slug: "sports", productCount: 30 },
      { id: "theater", name: "Theater", slug: "theater", productCount: 23 },
    ],
  },
  {
    id: "farming-agriculture",
    name: "Farming & Agriculture",
    slug: "farming-agriculture",
    description: "Agricultural products and farming equipment",
    icon: "🌾",
    productCount: 123,
    subcategories: [
      { id: "seeds", name: "Seeds", slug: "seeds", productCount: 45 },
      {
        id: "equipment",
        name: "Equipment",
        slug: "equipment",
        productCount: 56,
      },
      {
        id: "livestock",
        name: "Livestock",
        slug: "livestock",
        productCount: 22,
      },
    ],
  },
  {
    id: "health-beauty",
    name: "Health & Beauty",
    slug: "health-beauty",
    description: "Health and beauty products",
    icon: "�",
    productCount: 234,
    subcategories: [
      { id: "skincare", name: "Skincare", slug: "skincare", productCount: 89 },
      { id: "makeup", name: "Makeup", slug: "makeup", productCount: 67 },
      {
        id: "health-supplements",
        name: "Health Supplements",
        slug: "health-supplements",
        productCount: 78,
      },
    ],
  },
  {
    id: "home-garden",
    name: "Home & Garden",
    slug: "home-garden",
    description: "Home and garden items",
    icon: "🏡",
    productCount: 345,
    subcategories: [
      {
        id: "furniture",
        name: "Furniture",
        slug: "furniture",
        productCount: 123,
      },
      {
        id: "garden-tools",
        name: "Garden Tools",
        slug: "garden-tools",
        productCount: 89,
      },
      {
        id: "home-decor",
        name: "Home Decor",
        slug: "home-decor",
        productCount: 133,
      },
    ],
  },
  {
    id: "it-computers",
    name: "IT & Computers",
    slug: "it-computers",
    description: "Computer hardware and IT equipment",
    icon: "💻",
    productCount: 189,
    subcategories: [
      { id: "laptops", name: "Laptops", slug: "laptops", productCount: 67 },
      { id: "desktops", name: "Desktops", slug: "desktops", productCount: 45 },
      {
        id: "accessories",
        name: "Accessories",
        slug: "accessories",
        productCount: 77,
      },
    ],
  },
  {
    id: "jewellers",
    name: "Jewellers",
    slug: "jewellers",
    description: "Jewelry and precious items",
    icon: "💎",
    productCount: 156,
    subcategories: [
      { id: "gold", name: "Gold Jewelry", slug: "gold", productCount: 67 },
      {
        id: "silver",
        name: "Silver Jewelry",
        slug: "silver",
        productCount: 45,
      },
      {
        id: "precious-stones",
        name: "Precious Stones",
        slug: "precious-stones",
        productCount: 44,
      },
    ],
  },
  {
    id: "jobs",
    name: "Jobs",
    slug: "jobs",
    description: "Job opportunities and career listings",
    icon: "👔",
    productCount: 298,
    subcategories: [
      {
        id: "full-time",
        name: "Full Time",
        slug: "full-time",
        productCount: 156,
      },
      {
        id: "part-time",
        name: "Part Time",
        slug: "part-time",
        productCount: 89,
      },
      {
        id: "freelance",
        name: "Freelance",
        slug: "freelance",
        productCount: 53,
      },
    ],
  },
  {
    id: "mobile-phones-gadgets",
    name: "Mobile Phones & Gadgets",
    slug: "mobile-phones-gadgets",
    description: "Mobile phones and electronic gadgets",
    icon: "📱",
    productCount: 234,
    subcategories: [
      {
        id: "smartphones",
        name: "Smartphones",
        slug: "smartphones",
        productCount: 89,
      },
      { id: "tablets", name: "Tablets", slug: "tablets", productCount: 67 },
      {
        id: "smartwatches",
        name: "Smartwatches",
        slug: "smartwatches",
        productCount: 35,
      },
      {
        id: "phone-accessories",
        name: "Phone Accessories",
        slug: "phone-accessories",
        productCount: 43,
      },
    ],
  },
  {
    id: "music-musical-instruments",
    name: "Music & Musical Instruments",
    slug: "music-musical-instruments",
    description: "Musical instruments and music equipment",
    icon: "🎵",
    productCount: 145,
    subcategories: [
      { id: "guitars", name: "Guitars", slug: "guitars", productCount: 45 },
      {
        id: "keyboards",
        name: "Keyboards",
        slug: "keyboards",
        productCount: 35,
      },
      { id: "drums", name: "Drums", slug: "drums", productCount: 25 },
      {
        id: "audio-equipment",
        name: "Audio Equipment",
        slug: "audio-equipment",
        productCount: 40,
      },
    ],
  },
  {
    id: "office-supplies",
    name: "Office Supplies",
    slug: "office-supplies",
    description: "Office equipment and supplies",
    icon: "�",
    productCount: 123,
    subcategories: [
      {
        id: "stationery",
        name: "Stationery",
        slug: "stationery",
        productCount: 45,
      },
      {
        id: "office-furniture",
        name: "Office Furniture",
        slug: "office-furniture",
        productCount: 56,
      },
      {
        id: "electronics",
        name: "Office Electronics",
        slug: "electronics",
        productCount: 22,
      },
    ],
  },
  {
    id: "pets-animals",
    name: "Pets & Animals",
    slug: "pets-animals",
    description: "Pets and animal-related items",
    icon: "🐕",
    productCount: 89,
    subcategories: [
      { id: "dogs", name: "Dogs", slug: "dogs", productCount: 25 },
      { id: "cats", name: "Cats", slug: "cats", productCount: 20 },
      {
        id: "pet-supplies",
        name: "Pet Supplies",
        slug: "pet-supplies",
        productCount: 44,
      },
    ],
  },
  {
    id: "photography",
    name: "Photography",
    slug: "photography",
    description: "Photography equipment and services",
    icon: "📷",
    productCount: 78,
    subcategories: [
      { id: "cameras", name: "Cameras", slug: "cameras", productCount: 25 },
      { id: "lenses", name: "Lenses", slug: "lenses", productCount: 30 },
      {
        id: "accessories",
        name: "Accessories",
        slug: "accessories",
        productCount: 23,
      },
    ],
  },
  {
    id: "property",
    name: "Property",
    slug: "property",
    description: "Real estate and property listings",
    icon: "🏠",
    productCount: 145,
    subcategories: [
      { id: "houses", name: "Houses", slug: "houses", productCount: 67 },
      {
        id: "apartments",
        name: "Apartments",
        slug: "apartments",
        productCount: 78,
      },
    ],
  },
  {
    id: "sports-recreation",
    name: "Sports & Recreation",
    slug: "sports-recreation",
    description: "Sports equipment and recreational items",
    icon: "⚽",
    productCount: 234,
    subcategories: [
      {
        id: "fitness",
        name: "Fitness Equipment",
        slug: "fitness",
        productCount: 89,
      },
      {
        id: "outdoor-sports",
        name: "Outdoor Sports",
        slug: "outdoor-sports",
        productCount: 67,
      },
      {
        id: "indoor-games",
        name: "Indoor Games",
        slug: "indoor-games",
        productCount: 78,
      },
    ],
  },
  {
    id: "travel-tourism",
    name: "Travel & Tourism",
    slug: "travel-tourism",
    description: "Travel services and tourism",
    icon: "✈️",
    productCount: 56,
    subcategories: [
      {
        id: "packages",
        name: "Travel Packages",
        slug: "packages",
        productCount: 25,
      },
      { id: "hotels", name: "Hotels", slug: "hotels", productCount: 20 },
      {
        id: "transport",
        name: "Transport",
        slug: "transport",
        productCount: 11,
      },
    ],
  },
  {
    id: "electronics-appliances",
    name: "Electronics & Appliances",
    slug: "electronics-appliances",
    description: "Electronic devices and home appliances",
    icon: "🔌",
    productCount: 234,
    subcategories: [
      {
        id: "home-appliances",
        name: "Home Appliances",
        slug: "home-appliances",
        productCount: 89,
      },
      {
        id: "kitchen-appliances",
        name: "Kitchen Appliances",
        slug: "kitchen-appliances",
        productCount: 78,
      },
      {
        id: "air-conditioning",
        name: "Air Conditioning",
        slug: "air-conditioning",
        productCount: 35,
      },
      {
        id: "washing-machines",
        name: "Washing Machines",
        slug: "washing-machines",
        productCount: 32,
      },
    ],
  },
  {
    id: "education-training",
    name: "Education & Training",
    slug: "education-training",
    description: "Educational services and training programs",
    icon: "🎓",
    productCount: 123,
    subcategories: [
      { id: "courses", name: "Courses", slug: "courses", productCount: 45 },
      { id: "tutoring", name: "Tutoring", slug: "tutoring", productCount: 56 },
      {
        id: "materials",
        name: "Educational Materials",
        slug: "materials",
        productCount: 22,
      },
    ],
  },
  {
    id: "vehicles",
    name: "Vehicles",
    slug: "vehicles",
    description: "Cars, motorcycles, and other vehicles",
    icon: "🚗",
    productCount: 189,
    subcategories: [
      { id: "cars", name: "Cars", slug: "cars", productCount: 123 },
      {
        id: "motorcycles",
        name: "Motorcycles",
        slug: "motorcycles",
        productCount: 45,
      },
      { id: "bicycles", name: "Bicycles", slug: "bicycles", productCount: 21 },
    ],
  },
];

// Mock Products Data (Art & Crafts category)
const rawMockProducts: Omit<Product, "slug">[] = [
  {
    id: MOCK_UUIDS.PRODUCT_1,
    title: "Lord Krishna Canvas",
    description:
      "Beautiful hand-painted Lord Krishna canvas art piece with vibrant colors",
    price: 12345,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "art-crafts",
    subcategory: "painting",
    location: "Kathmandu",
    seller: {
      id: MOCK_UUIDS.SELLER_1,
      name: "John",
      rating: 4.5,
    },
    condition: "new",
    brand: "Handmade",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    delivery: {
      available: true,
      type: "both",
      cost: 200,
    },
    featured: true,
    status: "active",
  },
  {
    id: MOCK_UUIDS.PRODUCT_2,
    title: "Portrait Painting",
    description:
      "Custom portrait painting service available for personal and family portraits",
    price: 12345,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "art-crafts",
    subcategory: "painting",
    location: "Lalitpur",
    seller: {
      id: MOCK_UUIDS.SELLER_2,
      name: "John",
      rating: 4.8,
    },
    condition: "new",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: false,
    status: "active",
  },
  {
    id: MOCK_UUIDS.PRODUCT_3,
    title: "Clay Pots",
    description:
      "Traditional handmade clay pots perfect for home decoration and gardening",
    price: 12345,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "art-crafts",
    subcategory: "pottery",
    location: "Lalitpur",
    seller: {
      id: MOCK_UUIDS.SELLER_3,
      name: "John",
      rating: 4.2,
    },
    condition: "new",
    brand: "Traditional Crafts",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "home",
      cost: 150,
    },
    featured: false,
    status: "active",
  },
  {
    id: MOCK_UUIDS.PRODUCT_4,
    title: "Sculpture",
    description:
      "Handcrafted wooden sculpture representing traditional Nepali art",
    price: 12345,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "art-crafts",
    subcategory: "sculpture",
    location: "Lalitpur",
    seller: {
      id: "seller4",
      name: "John",
      rating: 4.7,
    },
    condition: "used",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 300,
    },
    featured: true,
    status: "active",
  },
  {
    id: "5",
    title: "Landscape Painting",
    description:
      "Scenic landscape painting of Himalayan mountains with beautiful sunset",
    price: 12345,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "art-crafts",
    subcategory: "painting",
    location: "Lalitpur",
    seller: {
      id: "seller5",
      name: "John",
      rating: 4.3,
    },
    condition: "used",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 250,
    },
    featured: false,
    status: "active",
  },
  {
    id: "6",
    title: "Bird Canvas",
    description: "Abstract bird canvas art with modern artistic interpretation",
    price: 12345,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "art-crafts",
    subcategory: "canvas",
    location: "Lalitpur",
    seller: {
      id: "seller6",
      name: "John",
      rating: 4.6,
    },
    condition: "refurbished",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "home",
      cost: 200,
    },
    featured: false,
    status: "active",
  },
  {
    id: "7",
    title: "Toyota Corolla 2020 - Excellent Condition",
    description:
      "Well maintained Toyota Corolla 2020 model. Single owner, all papers clean. Regular servicing done. No accidents. Excellent condition both interior and exterior. This car has been garage kept and driven only on weekends. All maintenance records available. Serious buyers only please. Contact condition both interior and exterior.",
    price: 69000,
    currency: "$ ",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "vehicles",
    subcategory: "cars",
    location: "Baneshwor, Kathmandu",
    seller: {
      id: "seller7",
      name: "John Doe",
      rating: 4.2,
    },
    condition: "used",
    brand: "Toyota",
    postedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: true,
    status: "active",
  },
  {
    id: "8",
    title: "Toyota Corolla Cross 2024 - Brand New",
    description:
      "Brand new Toyota Corolla Cross 2024 model with all latest features.",
    price: 69000,
    currency: "$ ",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "vehicles",
    subcategory: "cars",
    location: "Lalitpur",
    seller: {
      id: "seller8",
      name: "Toyota Dealer",
      rating: 4.8,
    },
    condition: "new",
    brand: "Toyota",
    postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 500,
    },
    featured: false,
    status: "active",
  },
  {
    id: "9",
    title: "Toyota Corolla Cross 2023 - Like New",
    description: "Almost new Toyota Corolla Cross 2023 with minimal usage.",
    price: 69000,
    currency: "$ ",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "vehicles",
    subcategory: "cars",
    location: "Bhaktapur",
    seller: {
      id: "seller9",
      name: "Car Dealer",
      rating: 4.5,
    },
    condition: "used",
    brand: "Toyota",
    postedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: false,
    status: "active",
  },
  {
    id: "10",
    title: "Toyota Corolla Cross 2022 - Well Maintained",
    description:
      "Well maintained Toyota Corolla Cross 2022 with service history.",
    price: 69000,
    currency: "$ ",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "vehicles",
    subcategory: "cars",
    location: "Pokhara",
    seller: {
      id: "seller10",
      name: "Private Seller",
      rating: 4.3,
    },
    condition: "used",
    brand: "Toyota",
    postedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: false,
    status: "active",
  },
  // Property Products
  {
    id: MOCK_UUIDS.PRODUCT_PROPERTY_1,
    title: "5000sq.ft Land",
    description: "Agricultural land with fertile soil, perfect for farming",
    price: 69000,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "property",
    subcategory: "houses",
    location: "Kathmandu Valley",
    seller: {
      id: MOCK_UUIDS.SELLER_PROP_1,
      name: "Land Dealer",
      rating: 4.8,
    },
    condition: "new",
    brand: "Prime Location",
    postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: true,
    status: "active",
  },
  {
    id: "property-2",
    title: "5000sq.ft Land",
    description: "Premium agricultural land with water access",
    price: 69000,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "property",
    subcategory: "houses",
    location: "Pokhara",
    seller: {
      id: "seller-prop2",
      name: "Property Expert",
      rating: 4.7,
    },
    condition: "new",
    brand: "Agricultural Land",
    postedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: true,
    status: "active",
  },
  {
    id: "property-3",
    title: "5000sq.ft Land",
    description: "Spacious land plot with road access",
    price: 69000,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "property",
    subcategory: "houses",
    location: "Chitwan",
    seller: {
      id: "seller-prop3",
      name: "Real Estate Pro",
      rating: 4.9,
    },
    condition: "new",
    brand: "Premium Plot",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: true,
    status: "active",
  },
  {
    id: "property-4",
    title: "5000sq.ft Land",
    description: "Beautiful land with mountain view",
    price: 69000,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "property",
    subcategory: "houses",
    location: "Bhaktapur",
    seller: {
      id: "seller-prop4",
      name: "Mountain View Properties",
      rating: 4.6,
    },
    condition: "new",
    brand: "Scenic Location",
    postedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: true,
    status: "active",
  },
  // Vehicle Products
  {
    id: MOCK_UUIDS.PRODUCT_VEHICLE_1,
    title: "Toyota Camry 2020",
    description: "Well-maintained sedan with low mileage",
    price: 25000,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "vehicles",
    subcategory: "cars",
    location: "Kathmandu",
    seller: {
      id: MOCK_UUIDS.SELLER_VEH_1,
      name: "Car Dealer",
      rating: 4.5,
    },
    condition: "used",
    brand: "Toyota",
    postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: true,
    status: "active",
  },
  {
    id: "vehicle-2",
    title: "Honda Civic 2019",
    description: "Reliable compact car in excellent condition",
    price: 22000,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "vehicles",
    subcategory: "cars",
    location: "Pokhara",
    seller: {
      id: "seller-veh2",
      name: "Auto Sales",
      rating: 4.7,
    },
    condition: "used",
    brand: "Honda",
    postedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: false,
      type: "pickup",
    },
    featured: true,
    status: "active",
  },
  // Electronics Products
  {
    id: MOCK_UUIDS.PRODUCT_ELECTRONICS_1,
    title: "iPhone 14 Pro",
    description: "Latest iPhone with 256GB storage",
    price: 999,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "electronics",
    subcategory: "mobile-phones",
    location: "Kathmandu",
    seller: {
      id: MOCK_UUIDS.SELLER_ELEC_1,
      name: "Tech Store",
      rating: 4.8,
    },
    condition: "new",
    brand: "Apple",
    postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 50,
    },
    featured: true,
    status: "active",
  },
  {
    id: "electronics-2",
    title: "Samsung Galaxy S23",
    description: "High-performance Android smartphone",
    price: 799,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "electronics",
    subcategory: "mobile-phones",
    location: "Lalitpur",
    seller: {
      id: "seller-elec2",
      name: "Mobile Hub",
      rating: 4.6,
    },
    condition: "new",
    brand: "Samsung",
    postedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 50,
    },
    featured: true,
    status: "active",
  },
  // Additional Products - 3 more entries
  {
    id: "art-crafts-6",
    title: "Handwoven Carpet",
    description:
      "Traditional Nepali handwoven carpet with intricate patterns and vibrant colors",
    price: 15000,
    currency: "Rs.",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "art-crafts",
    subcategory: "canvas",
    location: "Kathmandu",
    seller: {
      id: "seller-art6",
      name: "Carpet Artisan",
      rating: 4.9,
    },
    condition: "new",
    brand: "Traditional Weaves",
    postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 400,
    },
    featured: true,
    status: "active",
  },
  {
    id: "vehicle-3",
    title: "Yamaha R15 V4 2023",
    description:
      "Brand new sports motorcycle with advanced features and excellent performance",
    price: 4500,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "vehicles",
    subcategory: "motorcycles",
    location: "Pokhara",
    seller: {
      id: "seller-veh3",
      name: "Yamaha Showroom",
      rating: 4.8,
    },
    condition: "new",
    brand: "Yamaha",
    postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 200,
    },
    featured: true,
    status: "active",
  },
  {
    id: "electronics-3",
    title: "MacBook Pro M2 14-inch",
    description:
      "Latest MacBook Pro with M2 chip, 16GB RAM, 512GB SSD - Perfect for professionals",
    price: 2199,
    currency: "$",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "electronics",
    subcategory: "laptops",
    location: "Kathmandu",
    seller: {
      id: "seller-elec3",
      name: "Apple Store Nepal",
      rating: 4.9,
    },
    condition: "new",
    brand: "Apple",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 100,
    },
    featured: true,
    status: "active",
  },
  // Home & Garden Products
  {
    id: "home-1",
    title: "Modern Sofa Set",
    description: "Comfortable 3-seater sofa set with premium fabric upholstery",
    price: 45000,
    currency: "Rs.",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "home-garden",
    subcategory: "furniture",
    location: "Kathmandu",
    seller: {
      id: "seller-home1",
      name: "Furniture Palace",
      rating: 4.6,
    },
    condition: "new",
    brand: "Modern Living",
    postedAt: new Date("2024-01-15").toISOString(),
    delivery: {
      available: true,
      type: "home",
      cost: 500,
    },
    featured: true,
    status: "active",
  },
  {
    id: "home-2",
    title: "Garden Tool Set",
    description:
      "Complete gardening tool set with spade, rake, pruning shears and more",
    price: 3500,
    currency: "Rs.",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "home-garden",
    subcategory: "garden-tools",
    location: "Lalitpur",
    seller: {
      id: "seller-home2",
      name: "Green Thumb Store",
      rating: 4.4,
    },
    condition: "new",
    brand: "Garden Pro",
    postedAt: new Date("2024-01-18").toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 200,
    },
    featured: false,
    status: "active",
  },
  {
    id: "home-3",
    title: "Decorative Wall Mirror",
    description:
      "Elegant round wall mirror with golden frame - perfect for living room",
    price: 8500,
    currency: "Rs.",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "home-garden",
    subcategory: "home-decor",
    location: "Bhaktapur",
    seller: {
      id: "seller-home3",
      name: "Decor World",
      rating: 4.7,
    },
    condition: "new",
    brand: "Elegant Designs",
    postedAt: new Date("2024-01-20").toISOString(),
    delivery: {
      available: true,
      type: "home",
      cost: 300,
    },
    featured: true,
    status: "active",
  },
  {
    id: "home-4",
    title: "Dining Table Set",
    description:
      "Wooden dining table with 6 chairs - perfect for family gatherings",
    price: 35000,
    currency: "Rs.",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "home-garden",
    subcategory: "furniture",
    location: "Pokhara",
    seller: {
      id: "seller-home4",
      name: "Wood Craft Nepal",
      rating: 4.5,
    },
    condition: "new",
    brand: "Traditional Wood",
    postedAt: new Date("2024-01-22").toISOString(),
    delivery: {
      available: true,
      type: "both",
      cost: 800,
    },
    featured: false,
    status: "active",
  },
  {
    id: "home-5",
    title: "Indoor Plant Collection",
    description:
      "Set of 5 beautiful indoor plants including snake plant, pothos, and peace lily",
    price: 2500,
    currency: "Rs.",
    images: [
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
      "/assets/images/placeholders/placeholder.jpg",
    ],
    category: "home-garden",
    subcategory: "garden-tools",
    location: "Kathmandu",
    seller: {
      id: "seller-home5",
      name: "Plant Paradise",
      rating: 4.8,
    },
    condition: "new",
    brand: "Green Life",
    postedAt: new Date("2024-01-25").toISOString(),
    delivery: {
      available: true,
      type: "home",
      cost: 150,
    },
    featured: true,
    status: "active",
  },
  // Job listings
  {
    id: "job-1",
    title: "Senior Frontend Developer",
    description:
      "We're looking for a passionate Senior Frontend Developer to join our growing team. You'll be responsible for building modern, responsive web applications using React, TypeScript, and cutting-edge technologies.",
    price: 150000,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "jobs",
    subcategory: "full-time",
    location: "Kathmandu",
    seller: {
      id: "company1",
      name: "TechCorp Solutions",
      rating: 4.8,
    },
    condition: "new",
    brand: "TechCorp",
    postedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    delivery: {
      available: false,
      type: "pickup",
      cost: 0,
    },
    featured: true,
    status: "active",
  },
  {
    id: "job-2",
    title: "Digital Marketing Associate",
    description:
      "We are looking for a motivated and detail-oriented Digital Marketing Associate with 0-1 year of experience to join our growing team. You will assist in executing marketing campaigns, managing digital platforms, analyzing data, and supporting the team in day-to-day tasks.",
    price: 45000,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "jobs",
    subcategory: "full-time",
    location: "Lalitpur",
    seller: {
      id: "company2",
      name: "Ads! Digital",
      rating: 4.5,
    },
    condition: "used",
    brand: "Ads! Digital",
    postedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
    delivery: {
      available: false,
      type: "pickup",
      cost: 0,
    },
    featured: false,
    status: "active",
  },
  {
    id: "job-3",
    title: "Video Editing Intern",
    description:
      "Amoeba Labs is hiring a Video Editing Intern to support our digital storytelling efforts. This is a unique opportunity for aspiring editors to build their portfolio and work on exciting content in a fast-paced environment.",
    price: 25000,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "jobs",
    subcategory: "part-time",
    location: "Kathmandu",
    seller: {
      id: "company3",
      name: "Amoeba Labs",
      rating: 4.2,
    },
    condition: "new",
    brand: "Amoeba Labs",
    postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    delivery: {
      available: false,
      type: "pickup",
      cost: 0,
    },
    featured: false,
    status: "active",
  },
  {
    id: "job-4",
    title: "Frontend Developer",
    description:
      "Join our dynamic team as a Frontend Developer! We are seeking a skilled individual to build responsive and user-friendly web interfaces using modern JavaScript frameworks like React and Next.js. Collaborate with designers and backend developers to deliver high-quality products.",
    price: 80000,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "jobs",
    subcategory: "full-time",
    location: "Pokhara",
    seller: {
      id: "company4",
      name: "Tech Solutions Inc.",
      rating: 4.6,
    },
    condition: "new",
    brand: "Tech Solutions Inc.",
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    delivery: {
      available: false,
      type: "pickup",
      cost: 0,
    },
    featured: true,
    status: "active",
  },
];

// Mock Filter Options
export const mockFilterOptions: FilterOptions = {
  condition: ["new", "used", "refurbished"],
  type: [
    "Painting",
    "Sculpture",
    "Pottery",
    "Canvas Art",
    "Digital Art",
    "Handmade Crafts",
  ],
  location: [
    "Kathmandu",
    "Lalitpur",
    "Bhaktapur",
    "Pokhara",
    "Chitwan",
    "Biratnagar",
  ],
  delivery: ["home", "pickup"],
  brand: [
    "Handmade",
    "Traditional Crafts",
    "Modern Art Studio",
    "Local Artists",
    "Art Gallery",
  ],
  priceRange: {
    min: 0,
    max: 100000,
  },
  postedWithin: "7days",
};

// Utility functions for mock data
export const getProductsByCategory = (categoryId: string): Product[] => {
  return mockProducts.filter((product) => product.category === categoryId);
};

export const getCategoryById = (categoryId: string): Category | undefined => {
  return mockCategories.find((category) => category.id === categoryId);
};

export const searchMockProducts = (
  query: string,
  categoryId?: string
): Product[] => {
  const products = categoryId
    ? getProductsByCategory(categoryId)
    : mockProducts;

  if (!query) return products;

  return products.filter(
    (product) =>
      product.title.toLowerCase().includes(query.toLowerCase()) ||
      product.description.toLowerCase().includes(query.toLowerCase()) ||
      product.location.toLowerCase().includes(query.toLowerCase())
  );
};

// Process raw products to add slugs
const processProductsWithSlugs = (
  products: Omit<Product, "slug">[]
): Product[] => {
  return products.map((product) => ({
    ...product,
    slug: generateSlug(product.title, product.id),
  }));
};

// Export processed products with slugs
export const mockProducts: Product[] =
  processProductsWithSlugs(rawMockProducts);

// Utility function to find product by slug
export const getProductBySlug = (slug: string): Product | null => {
  return mockProducts.find((product) => product.slug === slug) || null;
};
